[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("PuzzlePanel"), PackedStringArray("Layout", "Patch Margin"), NodePath("PuzzlePanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/TitleLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer1"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/DescriptionLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer2"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/CipherLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer3"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/InputContainer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/InputContainer/InputLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/InputContainer/InputField"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/InputContainer/SubmitButton"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer4"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/HintButton"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton"), PackedStringArray("Layout")]
resource_unfolds=[]
nodes_folded=[]
