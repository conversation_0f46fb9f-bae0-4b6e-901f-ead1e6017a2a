extends Control
class_name OrderTestPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var question_label: Label = $PuzzlePanel/VBoxContainer/QuestionLabel
@onready var answer_field: LineEdit = $PuzzlePanel/VBoxContainer/AnswerContainer/AnswerField
@onready var submit_button: Button = $PuzzlePanel/VBoxContainer/AnswerContainer/SubmitButton
@onready var progress_label: Label = $PuzzlePanel/VBoxContainer/ProgressLabel
@onready var hint_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var questions: Array[Dictionary] = [
	{
		"question": "Aký kov sa používal v alchýmii na čistenie duše a zároveň poškodzuje telo nemŕtvych?",
		"answer": "STRIEBRO",
		"alternatives": ["STRIEBRO", "SILVER"]
	},
	{
		"question": "Ktoré nebeské teleso alchymisti spájali so striebrom a ktoré ovláda príliv a odliv?",
		"answer": "MESIAC",
		"alternatives": ["MESIAC", "LUNA", "MOON"]
	},
	{
		"question": "Aký bylinný výťažok sa podľa legiend prikladal na hroby, aby zadržal prekliatych pod zemou?",
		"answer": "CESNAK",
		"alternatives": ["CESNAK", "GARLIC"]
	}
]

var current_question: int = 0
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripojenie signálov
	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	if answer_field:
		answer_field.text_submitted.connect(_on_text_submitted)
	
	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Skúška Rádu"
	
	if description_label:
		description_label.text = "[center][b]Viktor:[/b] Tri otázky. Tri odpovede.[/center]\n\nKaždá z iného sveta – kov, hviezda, bylina.\nDokážte, že patríte k Rádu:"
	
	current_question = 0
	display_current_question()

func show_puzzle():
	show()
	if answer_field:
		answer_field.grab_focus()

func display_current_question():
	if current_question < questions.size():
		var question_data = questions[current_question]
		
		if question_label:
			question_label.text = "Otázka " + str(current_question + 1) + ":\n" + question_data.question
		
		if progress_label:
			progress_label.text = "Otázka " + str(current_question + 1) + " z " + str(questions.size())
		
		if answer_field:
			answer_field.text = ""
			answer_field.placeholder_text = "Zadajte odpoveď..."
			answer_field.grab_focus()

func _on_submit_pressed():
	check_answer()

func _on_text_submitted(text: String):
	check_answer()

func check_answer():
	if not answer_field or current_question >= questions.size():
		return
	
	var user_answer = normalize_text(answer_field.text)
	var question_data = questions[current_question]
	
	# Kontrola správnej odpovede
	var is_correct = false
	for alternative in question_data.alternatives:
		if user_answer == normalize_text(alternative):
			is_correct = true
			break
	
	if is_correct:
		# Správna odpoveď
		current_question += 1
		
		if current_question >= questions.size():
			# Všetky otázky zodpovedané
			puzzle_solved.emit()
			hide()
		else:
			# Ďalšia otázka
			show_success_feedback()
			await get_tree().create_timer(1.0).timeout
			display_current_question()
	else:
		# Nesprávna odpoveď
		show_error_feedback()

func normalize_text(text: String) -> String:
	# Odstránenie diakritiky a normalizácia
	var normalized = text.to_upper().strip_edges()
	
	# Základná náhrada diakritiky
	var replacements = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E", "Ě": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ů": "U", "Ý": "Y",
		"Ž": "Z"
	}
	
	for old_char in replacements:
		normalized = normalized.replace(old_char, replacements[old_char])
	
	return normalized

func show_success_feedback():
	if answer_field:
		answer_field.modulate = Color.GREEN
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 1.0)

func show_error_feedback():
	if answer_field:
		answer_field.modulate = Color.RED
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 0.5)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Tri svety - kov, hviezda, bylina. Každý má svoju moc proti temnote."
		2:
			return "Kov čistí duše, hviezda ovláda vody, bylina chráni hroby."
		3:
			return "Striebro, Mesiac, Cesnak - základy každého lovca upírov."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(hint_text: String):
	var dialog = AcceptDialog.new()
	add_child(dialog)
	dialog.dialog_text = hint_text
	dialog.title = "Nápoveda"
	dialog.popup_centered()
	
	# Automatické odstránenie dialógu
	dialog.confirmed.connect(func(): dialog.queue_free())
	dialog.close_requested.connect(func(): dialog.queue_free())

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
