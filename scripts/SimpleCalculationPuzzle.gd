extends Control
class_name SimpleCalculationPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var answer_field: LineEdit = $PuzzlePanel/VBoxContainer/AnswerContainer/AnswerField
@onready var submit_button: Button = $PuzzlePanel/VBoxContainer/AnswerContainer/SubmitButton
@onready var hint_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_answer: int = 1576
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# <PERSON>rip<PERSON><PERSON><PERSON> sign<PERSON><PERSON>
	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	if answer_field:
		answer_field.text_submitted.connect(_on_text_submitted)
	
	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Van Helsingov denník"
	
	if description_label:
		description_label.text = "[center]V denníku je poznámka:[/center]\n\n\"Isabelle mala 20 rokov pri sobáši roku 1596.\nV ktorom roku sa narodila?\""
	
	# Nastavenie input fieldu len na číslice
	if answer_field:
		answer_field.placeholder_text = "Zadajte rok (4 číslice)..."

func show_puzzle():
	show()
	if answer_field:
		answer_field.grab_focus()

func _on_submit_pressed():
	check_answer()

func _on_text_submitted(text: String):
	check_answer()

func check_answer():
	if not answer_field:
		return
	
	var user_input = answer_field.text.strip_edges()
	
	# Kontrola, či je vstup číselný
	if not user_input.is_valid_int():
		show_error_feedback("Zadajte platný rok!")
		return
	
	var user_answer = user_input.to_int()
	
	if user_answer == correct_answer:
		# Správna odpoveď
		puzzle_solved.emit()
		hide()
	else:
		# Nesprávna odpoveď
		show_error_feedback("Nesprávny rok!")

func show_error_feedback(message: String = ""):
	if answer_field:
		answer_field.modulate = Color.RED
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 0.5)
	
	if message != "":
		show_hint_dialog(message)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Je to jednoduchá matematika."
		2:
			return "Od roku sobáša odpočítajte vek."
		3:
			return "1596 - 20 = ?"
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(hint_text: String):
	var dialog = AcceptDialog.new()
	add_child(dialog)
	dialog.dialog_text = hint_text
	dialog.title = "Nápoveda"
	dialog.popup_centered()
	
	# Automatické odstránenie dialógu
	dialog.confirmed.connect(func(): dialog.queue_free())
	dialog.close_requested.connect(func(): dialog.queue_free())

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
