extends Control

@onready var scroll_container: ScrollContainer = $VBoxContainer/ScrollContainer
@onready var chapters_container: VBoxContainer = $VBoxContainer/ScrollContainer/ChaptersContainer
@onready var back_button: Button = $VBoxContainer/BackButton

var chapter_buttons: Array[Button] = []

func _ready():
	back_button.pressed.connect(_on_back_pressed)
	create_chapter_buttons()

func create_chapter_buttons():
	# Vyčistiť existujúce tlačidlá
	for child in chapters_container.get_children():
		child.queue_free()
	
	chapter_buttons.clear()
	
	# Vytvoriť tlačidlá pre každú kapitolu
	for chapter_num in range(1, 8):  # 6 kapitol + epilóg
		var chapter_button = create_chapter_button(chapter_num)
		chapters_container.add_child(chapter_button)
		chapter_buttons.append(chapter_button)
	
	# Nastaviť fokus na prvé dostupné tlačidlo
	if chapter_buttons.size() > 0:
		chapter_buttons[0].grab_focus()

func create_chapter_button(chapter_number: int) -> Control:
	var chapter_container = VBoxContainer.new()
	chapter_container.custom_minimum_size = Vector2(0, 150)
	
	# Panel pre kapitolu
	var chapter_panel = NinePatchRect.new()
	chapter_panel.texture = preload("res://assets/Scalable screen/Scalable_2.png")
	chapter_panel.patch_margin_left = 16
	chapter_panel.patch_margin_top = 16
	chapter_panel.patch_margin_right = 16
	chapter_panel.patch_margin_bottom = 16
	chapter_panel.custom_minimum_size = Vector2(0, 120)
	
	# Kontajner pre obsah
	var content_container = HBoxContainer.new()
	content_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	content_container.add_theme_constant_override("margin_left", 20)
	content_container.add_theme_constant_override("margin_right", 20)
	content_container.add_theme_constant_override("margin_top", 10)
	content_container.add_theme_constant_override("margin_bottom", 10)
	
	# Informácie o kapitole
	var info_container = VBoxContainer.new()
	info_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	
	var chapter_info = GameManager.chapter_info[chapter_number]
	
	# Názov kapitoly
	var title_label = Label.new()
	title_label.text = chapter_info.title
	title_label.add_theme_font_size_override("font_size", 18)
	
	# Popis kapitoly
	var desc_label = Label.new()
	desc_label.text = chapter_info.description
	desc_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	desc_label.add_theme_font_size_override("font_size", 14)
	
	# Stav kapitoly
	var status_label = Label.new()
	if GameManager.is_chapter_unlocked(chapter_number):
		if chapter_number in GameManager.completed_chapters:
			status_label.text = "✓ Dokončené"
			status_label.add_theme_color_override("font_color", Color.GREEN)
		else:
			status_label.text = "Dostupné"
			status_label.add_theme_color_override("font_color", Color.YELLOW)
	else:
		status_label.text = "🔒 Uzamknuté"
		status_label.add_theme_color_override("font_color", Color.GRAY)
	
	info_container.add_child(title_label)
	info_container.add_child(desc_label)
	info_container.add_child(status_label)
	
	# Tlačidlo pre spustenie kapitoly
	var play_button = Button.new()
	play_button.text = "Hrať"
	play_button.custom_minimum_size = Vector2(120, 60)
	play_button.disabled = not GameManager.is_chapter_unlocked(chapter_number)
	
	# Pripojenie signálu
	play_button.pressed.connect(_on_chapter_selected.bind(chapter_number))
	
	content_container.add_child(info_container)
	content_container.add_child(play_button)
	
	chapter_panel.add_child(content_container)
	chapter_container.add_child(chapter_panel)
	
	# Pridať medzeru
	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 10)
	chapter_container.add_child(spacer)
	
	return chapter_container

func _on_chapter_selected(chapter_number: int):
	if GameManager.is_chapter_unlocked(chapter_number):
		GameManager.go_to_chapter(chapter_number)

func _on_back_pressed():
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
