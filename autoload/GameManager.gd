extends Node

# Singleton pre riadenie hry
signal chapter_completed(chapter_number: int)
signal puzzle_completed(chapter_number: int, puzzle_number: int)

# Stav hry
var current_chapter: int = 1
var completed_chapters: Array[int] = []
var completed_puzzles: Dictionary = {} # {chapter_number: [puzzle1, puzzle2]}
var game_settings: Dictionary = {
	"master_volume": 1.0,
	"music_volume": 1.0,
	"sfx_volume": 1.0,
	"fullscreen": false
}

# Informácie o kapitolách
var chapter_info: Dictionary = {
	1: {
		"title": "Kapitola 1: Záhadný začiatok",
		"description": "Marec 1894. Cesta k zámku Van Helsinga cez karpatské horstvo. Rozlúštite Van Helsingove šifry a nájdite cestu k zámku.",
		"puzzles": ["Van Helsingova šifra", "Cesta lesom"]
	},
	2: {
		"title": "Kapitola 2: Hlbšie do temnoty",
		"description": "<PERSON>rána zám<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON>, že patr<PERSON>te k Rádu a vstúpte do Van Helsingovho sídla.",
		"puzzles": ["<PERSON><PERSON><PERSON><PERSON> nápis", "Skúška Rádu"]
	},
	3: {
		"title": "Kapitola 3: Stredoveké tajomstvá",
		"description": "Odhaľte starodávne záhady ukryté v hradných múroch.",
		"puzzles": ["Rytierske symboly", "Alchymistická formula"]
	},
	4: {
		"title": "Kapitola 4: Magické rituály",
		"description": "Vstúpte do sveta mágie a nadprirodzených síl.",
		"puzzles": ["Runy moci", "Kúzelné kruhy"]
	},
	5: {
		"title": "Kapitola 5: Posledné varovanie",
		"description": "Blížite sa k finále. Posledné prekážky pred pravdou.",
		"puzzles": ["Časová paradox", "Zrkadlové bludiště"]
	},
	6: {
		"title": "Kapitola 6: Konečné odhalenie",
		"description": "Všetky záhady sa spájajú. Pripravte sa na finálne odhalenie.",
		"puzzles": ["Majstrovský kľúč", "Posledná hádanka"]
	},
	7: {
		"title": "Epilóg: Nový začiatok",
		"description": "Príbeh sa končí, ale dedičstvo pokračuje...",
		"puzzles": []
	}
}

func _ready():
	load_game_data()

# Navigácia medzi scénami
func go_to_main_menu():
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func go_to_chapters():
	get_tree().change_scene_to_file("res://scenes/ChaptersMenu.tscn")

func go_to_chapter(chapter_number: int):
	current_chapter = chapter_number
	get_tree().change_scene_to_file("res://scenes/Chapter" + str(chapter_number) + ".tscn")

func go_to_settings():
	get_tree().change_scene_to_file("res://scenes/SettingsMenu.tscn")

func go_to_about():
	get_tree().change_scene_to_file("res://scenes/AboutGame.tscn")

# Správa progresu
func complete_puzzle(chapter_number: int, puzzle_number: int):
	if not completed_puzzles.has(chapter_number):
		completed_puzzles[chapter_number] = []
	
	if puzzle_number not in completed_puzzles[chapter_number]:
		completed_puzzles[chapter_number].append(puzzle_number)
		puzzle_completed.emit(chapter_number, puzzle_number)
	
	# Ak sú dokončené oba hlavolamy, kapitola je hotová
	if completed_puzzles[chapter_number].size() >= 2:
		complete_chapter(chapter_number)

func complete_chapter(chapter_number: int):
	if chapter_number not in completed_chapters:
		completed_chapters.append(chapter_number)
		chapter_completed.emit(chapter_number)
	save_game_data()

func is_chapter_unlocked(chapter_number: int) -> bool:
	if chapter_number == 1:
		return true
	return (chapter_number - 1) in completed_chapters

func is_puzzle_completed(chapter_number: int, puzzle_number: int) -> bool:
	if not completed_puzzles.has(chapter_number):
		return false
	return puzzle_number in completed_puzzles[chapter_number]

# Ukladanie a načítavanie
func save_game_data():
	var save_data = {
		"completed_chapters": completed_chapters,
		"completed_puzzles": completed_puzzles,
		"game_settings": game_settings
	}
	
	var save_file = FileAccess.open("user://savegame.save", FileAccess.WRITE)
	if save_file:
		save_file.store_string(JSON.stringify(save_data))
		save_file.close()

func load_game_data():
	var save_file = FileAccess.open("user://savegame.save", FileAccess.READ)
	if save_file:
		var json_string = save_file.get_as_text()
		save_file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			var save_data = json.data
			completed_chapters = save_data.get("completed_chapters", [])
			completed_puzzles = save_data.get("completed_puzzles", {})
			game_settings = save_data.get("game_settings", game_settings)

# Nastavenia
func update_setting(setting_name: String, value):
	game_settings[setting_name] = value
	apply_settings()
	save_game_data()

func apply_settings():
	# Aplikovanie nastavení
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Master"), 
		linear_to_db(game_settings.master_volume))
	
	if game_settings.fullscreen:
		DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
	else:
		DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
