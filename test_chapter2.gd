extends SceneTree

func _init():
	print("=== TEST KAPITOLY 2 ===")
	
	# Test načítania GameManager
	var game_manager = preload("res://autoload/GameManager.gd").new()
	print("GameManager načítaný")
	
	# Test informácií o kapitole 2
	var chapter_info = game_manager.chapter_info[2]
	print("Info o kapitole 2: ", chapter_info)
	
	# Test odomknutia kapitoly 2
	var is_unlocked = game_manager.is_chapter_unlocked(2)
	print("Je kapitola 2 odomknutá: ", is_unlocked)
	
	# Test načítania scény kapitoly 2
	var chapter2_scene = load("res://scenes/Chapter2.tscn")
	if chapter2_scene:
		print("Chapter2.tscn úspešne načítaný")
		var instance = chapter2_scene.instantiate()
		print("Chapter2 instance vytvorená: ", instance)
		print("Chapter number: ", instance.chapter_number)
	else:
		print("CHYBA: Nemožno načítať Chapter2.tscn")
	
	# Test načítania DialogueSystem
	var dialogue_scene = load("res://scenes/DialogueSystem.tscn")
	if dialogue_scene:
		print("DialogueSystem.tscn úspešne načítaný")
	else:
		print("CHYBA: Nemožno načítať DialogueSystem.tscn")
	
	# Test načítania hlavolamov
	var blood_scene = load("res://scenes/BloodInscriptionPuzzle.tscn")
	if blood_scene:
		print("BloodInscriptionPuzzle.tscn úspešne načítaný")
	else:
		print("CHYBA: Nemožno načítať BloodInscriptionPuzzle.tscn")
	
	var order_scene = load("res://scenes/OrderTestPuzzle.tscn")
	if order_scene:
		print("OrderTestPuzzle.tscn úspešne načítaný")
	else:
		print("CHYBA: Nemožno načítať OrderTestPuzzle.tscn")
	
	print("=== KONIEC TESTU ===")
	quit()
